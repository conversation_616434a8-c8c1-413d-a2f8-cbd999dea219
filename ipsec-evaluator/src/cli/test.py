"""
Test execution CLI commands.

This module provides commands for executing IPsec compliance tests including:
- Single test execution
- Batch test execution
- Test planning and dry-run capabilities
- Progress tracking and monitoring
- Result management
"""

from pathlib import Path
from typing import Optional, List
from datetime import datetime

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Confirm

from ..utils.configuration import IPsecEvaluatorConfigParser, ConfigurationError
from ..utils.logging import get_logger
from ..engine.orchestrator import EnhancedOrchestrator

# Test subcommand group
test_app = typer.Typer(
    name="test",
    help="Test execution commands",
    rich_markup_mode="rich"
)

console = Console()
logger = get_logger(__name__)


@test_app.command("run")
def test_run(
    scenario: Optional[str] = typer.Argument(None, help="Test scenario name to execute"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file"),
    template: Optional[str] = typer.Option(None, "--template", "-t", help="Use configuration template"),
    mode: str = typer.Option("both", "--mode", "-m", help="Test mode (initiator, responder, both)"),
    output_dir: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be executed without running"),
    compliance_check: bool = typer.Option(True, "--compliance/--no-compliance", help="Enable compliance checking"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    timeout: Optional[int] = typer.Option(None, "--timeout", help="Test timeout in seconds")
):
    """Run IPsec compliance tests."""
    
    try:
        # Load configuration
        if config_file:
            if not config_file.exists():
                console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
                raise typer.Exit(1)
            parser = IPsecEvaluatorConfigParser(config_files=[config_file])
        elif template:
            parser = IPsecEvaluatorConfigParser.from_template(template)
        else:
            # Use default configuration
            parser = IPsecEvaluatorConfigParser.from_template('basic')
        
        config = parser.get_test_configuration()
        
        # Override configuration with CLI options
        if output_dir:
            config.global_config.results_dir = str(output_dir)
        if verbose:
            config.global_config.verbose = verbose
        if timeout:
            config.global_config.test_timeout = timeout
        
        # Show test plan
        if dry_run:
            _show_test_plan(config, scenario, mode, compliance_check)
            return
        
        # Create orchestrator
        orchestrator = EnhancedOrchestrator(config)
        
        console.print("[bold blue]🚀 Starting IPsec Compliance Tests[/bold blue]")
        console.print(f"[cyan]Configuration:[/cyan] {config_file or f'template:{template}' or 'default'}")
        console.print(f"[cyan]Mode:[/cyan] {mode}")
        console.print(f"[cyan]Output:[/cyan] {config.global_config.results_dir}")
        
        if scenario:
            console.print(f"[cyan]Scenario:[/cyan] {scenario}")
        else:
            console.print("[cyan]Scenario:[/cyan] All available scenarios")
        
        # Confirm execution
        if not Confirm.ask("Execute this test?"):
            console.print("[yellow]Test execution cancelled[/yellow]")
            return
        
        # Execute tests with progress tracking
        _execute_tests_with_progress(orchestrator, scenario, mode, compliance_check)
        
    except ConfigurationError as e:
        console.print(f"[red]Configuration Error:[/red] {e}")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@test_app.command("batch")
def test_batch(
    scenarios_file: Path = typer.Argument(..., help="File containing list of scenarios to run"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file"),
    template: Optional[str] = typer.Option(None, "--template", "-t", help="Use configuration template"),
    mode: str = typer.Option("both", "--mode", "-m", help="Test mode (initiator, responder, both)"),
    output_dir: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    parallel: int = typer.Option(1, "--parallel", "-p", help="Number of parallel test executions"),
    continue_on_error: bool = typer.Option(False, "--continue", help="Continue execution on test failures"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output")
):
    """Execute multiple IPsec compliance tests from a file."""
    
    if not scenarios_file.exists():
        console.print(f"[red]Error:[/red] Scenarios file not found: {scenarios_file}")
        raise typer.Exit(1)
    
    # Read scenarios from file
    try:
        with open(scenarios_file, "r") as f:
            scenarios = [
                line.strip() for line in f if line.strip() and not line.startswith("#")
            ]
    except Exception as e:
        console.print(f"[red]Error reading scenarios file:[/red] {e}")
        raise typer.Exit(1)
    
    if not scenarios:
        console.print("[yellow]No scenarios found in file[/yellow]")
        return
    
    try:
        # Load configuration
        if config_file:
            if not config_file.exists():
                console.print(f"[red]Error:[/red] Configuration file not found: {config_file}")
                raise typer.Exit(1)
            parser = IPsecEvaluatorConfigParser(config_files=[config_file])
        elif template:
            parser = IPsecEvaluatorConfigParser.from_template(template)
        else:
            parser = IPsecEvaluatorConfigParser.from_template('basic')
        
        config = parser.get_test_configuration()
        
        # Override configuration with CLI options
        if output_dir:
            config.global_config.results_dir = str(output_dir)
        if verbose:
            config.global_config.verbose = verbose
        
        console.print("[bold blue]🚀 Starting Batch Test Execution[/bold blue]")
        console.print(f"[cyan]Scenarios:[/cyan] {len(scenarios)} tests")
        console.print(f"[cyan]Parallel:[/cyan] {parallel}")
        console.print(f"[cyan]Mode:[/cyan] {mode}")
        console.print(f"[cyan]Output:[/cyan] {config.global_config.results_dir}")
        
        # Show scenario list
        _show_scenario_list(scenarios)
        
        # Confirm execution
        if not Confirm.ask(f"Execute {len(scenarios)} tests?"):
            console.print("[yellow]Batch execution cancelled[/yellow]")
            return
        
        # Execute batch tests
        _execute_batch_tests(config, scenarios, mode, parallel, continue_on_error)
        
    except Exception as e:
        console.print(f"[red]Error:[/red] {e}")
        raise typer.Exit(1)


@test_app.command("list")
def test_list(
    results_dir: Optional[Path] = typer.Option(None, "--dir", "-d", help="Results directory path"),
    limit: int = typer.Option(10, "--limit", "-l", help="Maximum number of results to show"),
    status_filter: Optional[str] = typer.Option(None, "--status", help="Filter by status (passed, failed, running)")
):
    """List recent test executions and results."""
    
    if results_dir is None:
        results_dir = Path("results")
    
    console.print(f"[bold blue]Recent Test Executions[/bold blue]")
    console.print(f"Directory: [dim]{results_dir}[/dim]")
    
    if not results_dir.exists():
        console.print("[yellow]No results directory found[/yellow]")
        console.print("[yellow]Tip:[/yellow] Run tests to generate results")
        return
    
    # Find result files
    result_files = list(results_dir.glob("*.json"))
    
    if not result_files:
        console.print("[yellow]No result files found[/yellow]")
        console.print("[yellow]Tip:[/yellow] Run tests to generate results")
        return
    
    # Sort by modification time (newest first)
    result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    table = Table(title="Recent Test Results", show_header=True)
    table.add_column("Timestamp", style="cyan")
    table.add_column("Test", style="blue")
    table.add_column("Status", style="green")
    table.add_column("Duration", style="yellow")
    table.add_column("File", style="white")
    
    for file_path in result_files[:limit]:
        try:
            stat = file_path.stat()
            timestamp = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            # Parse basic info from filename or file content
            test_name = file_path.stem
            status = "completed"  # Would parse from file content
            duration = "N/A"  # Would parse from file content
            
            # Apply status filter
            if status_filter and status_filter != status:
                continue
            
            table.add_row(timestamp, test_name, status, duration, file_path.name)
            
        except Exception as e:
            table.add_row("", file_path.name, f"[red]Error: {e}[/red]", "", "")
    
    console.print(table)


def _show_test_plan(config, scenario: Optional[str], mode: str, compliance_check: bool):
    """Show what would be executed in dry-run mode."""
    console.print("[bold blue]🔍 Test Execution Plan (Dry Run)[/bold blue]")
    
    # Configuration summary
    config_table = Table(title="Configuration Summary")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="white")
    
    config_table.add_row("Max Concurrent Tests", str(config.global_config.max_concurrent_tests))
    config_table.add_row("Test Timeout", f"{config.global_config.test_timeout}s")
    config_table.add_row("Results Directory", config.global_config.results_dir)
    config_table.add_row("Initiator IP", str(config.network.initiator_ip))
    config_table.add_row("Responder IP", str(config.network.responder_ip))
    config_table.add_row("IKE Port", str(config.network.ike_port))
    
    console.print(config_table)
    
    # Test plan
    console.print("\n[bold]Test Execution Plan:[/bold]")
    console.print(f"• Mode: [cyan]{mode}[/cyan]")
    if scenario:
        console.print(f"• Scenario: [cyan]{scenario}[/cyan]")
    else:
        console.print("• Scenario: [cyan]All available scenarios[/cyan]")
    console.print(f"• Compliance Check: [cyan]{'Enabled' if compliance_check else 'Disabled'}[/cyan]")
    
    # Crypto configuration
    crypto_table = Table(title="Cryptographic Configuration")
    crypto_table.add_column("Algorithm Type", style="cyan")
    crypto_table.add_column("Algorithms", style="white")
    
    crypto_table.add_row("IKE Encryption", ", ".join(config.crypto.ike_encryption))
    crypto_table.add_row("IKE Integrity", ", ".join(config.crypto.ike_integrity))
    crypto_table.add_row("IKE PRF", ", ".join(config.crypto.ike_prf))
    crypto_table.add_row("IKE DH Groups", ", ".join(map(str, config.crypto.ike_dh_groups)))
    crypto_table.add_row("ESP Encryption", ", ".join(config.crypto.esp_encryption))
    crypto_table.add_row("ESP Integrity", ", ".join(config.crypto.esp_integrity))
    
    console.print(crypto_table)


def _show_scenario_list(scenarios: List[str]):
    """Show list of scenarios to be executed."""
    console.print("\n[bold]Scenarios to Execute:[/bold]")
    
    for i, scenario in enumerate(scenarios, 1):
        console.print(f"  {i:2d}. [cyan]{scenario}[/cyan]")


def _execute_tests_with_progress(orchestrator, scenario: Optional[str], mode: str, compliance_check: bool):
    """Execute tests with progress tracking."""
    console.print("\n[yellow]⚠️  Test execution integration pending[/yellow]")
    console.print("This will be implemented with the full orchestrator integration.")
    
    # Simulate progress for demonstration
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        
        task = progress.add_task("Preparing test execution...", total=100)
        
        # Simulate preparation
        progress.update(task, advance=20, description="Loading configuration...")
        progress.update(task, advance=20, description="Initializing crypto modules...")
        progress.update(task, advance=20, description="Setting up network...")
        progress.update(task, advance=20, description="Starting orchestrator...")
        progress.update(task, advance=20, description="Test execution ready")
    
    console.print("[green]✓[/green] Test execution simulation completed")


def _execute_batch_tests(config, scenarios: List[str], mode: str, parallel: int, continue_on_error: bool):
    """Execute batch tests with progress tracking."""
    console.print("\n[yellow]⚠️  Batch test execution integration pending[/yellow]")
    console.print("This will be implemented with the full orchestrator integration.")
    
    # Simulate batch execution
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        
        batch_task = progress.add_task("Executing batch tests...", total=len(scenarios))
        
        for i, scenario in enumerate(scenarios):
            progress.update(
                batch_task, 
                advance=1, 
                description=f"Executing scenario {i+1}/{len(scenarios)}: {scenario}"
            )
    
    console.print(f"[green]✓[/green] Batch execution simulation completed ({len(scenarios)} scenarios)")
