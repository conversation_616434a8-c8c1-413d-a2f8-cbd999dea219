"""
Scenario loading and registry management for IPsec Evaluator.

This module provides functionality to load, register, and manage test scenarios
from various sources including files, directories, and remote locations.
"""

import os
import glob
from pathlib import Path
from typing import Dict, List, Optional, Union, Set, Any
from dataclasses import dataclass, field

from .parser import TestParser, ScenarioParsingError
from ..models.tests import Test, Scenario
from ..models.base import TestMode
from ..utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ScenarioMetadata:
    """Metadata for a loaded scenario."""
    
    name: str
    description: str
    version: str
    file_path: Path
    anssi_requirement: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    compliance_level: str = "standard"
    created_at: Optional[str] = None
    modified_at: Optional[str] = None


class ScenarioRegistry:
    """
    Registry for managing loaded test scenarios.
    
    Provides centralized access to all loaded scenarios with filtering,
    searching, and metadata management capabilities.
    """

    def __init__(self):
        """Initialize the scenario registry."""
        self.tests: Dict[str, Test] = {}
        self.metadata: Dict[str, ScenarioMetadata] = {}
        self.tags_index: Dict[str, Set[str]] = {}
        self.compliance_index: Dict[str, Set[str]] = {}
        
        logger.debug("Initialized ScenarioRegistry")

    def register_test(self, test: Test, metadata: ScenarioMetadata) -> None:
        """
        Register a test in the registry.
        
        Args:
            test: Test object to register
            metadata: Metadata for the test
        """
        test_name = test.name
        
        if test_name in self.tests:
            logger.warning(f"Overwriting existing test '{test_name}'")
        
        self.tests[test_name] = test
        self.metadata[test_name] = metadata
        
        # Update indexes
        self._update_tags_index(test_name, metadata.tags)
        self._update_compliance_index(test_name, metadata.compliance_level)
        
        logger.info(f"Registered test '{test_name}' with {len(metadata.tags)} tags")

    def _update_tags_index(self, test_name: str, tags: List[str]) -> None:
        """Update the tags index."""
        for tag in tags:
            if tag not in self.tags_index:
                self.tags_index[tag] = set()
            self.tags_index[tag].add(test_name)

    def _update_compliance_index(self, test_name: str, compliance_level: str) -> None:
        """Update the compliance index."""
        if compliance_level not in self.compliance_index:
            self.compliance_index[compliance_level] = set()
        self.compliance_index[compliance_level].add(test_name)

    def get_test(self, name: str) -> Optional[Test]:
        """Get a test by name."""
        return self.tests.get(name)

    def get_metadata(self, name: str) -> Optional[ScenarioMetadata]:
        """Get metadata for a test."""
        return self.metadata.get(name)

    def list_tests(self) -> List[str]:
        """Get list of all registered test names."""
        return list(self.tests.keys())

    def find_tests_by_tag(self, tag: str) -> List[str]:
        """Find tests by tag."""
        return list(self.tags_index.get(tag, set()))

    def find_tests_by_compliance(self, compliance_level: str) -> List[str]:
        """Find tests by compliance level."""
        return list(self.compliance_index.get(compliance_level, set()))

    def find_tests_by_mode(self, mode: TestMode) -> List[str]:
        """Find tests that support a specific mode."""
        matching_tests = []
        
        for test_name, test in self.tests.items():
            if test.has_scenarios_for_mode(mode):
                matching_tests.append(test_name)
        
        return matching_tests

    def search_tests(
        self, 
        name_pattern: Optional[str] = None,
        tags: Optional[List[str]] = None,
        compliance_level: Optional[str] = None,
        mode: Optional[TestMode] = None
    ) -> List[str]:
        """
        Search tests with multiple criteria.
        
        Args:
            name_pattern: Pattern to match test names (supports wildcards)
            tags: List of tags (test must have all tags)
            compliance_level: Required compliance level
            mode: Required test mode support
            
        Returns:
            List of matching test names
        """
        matching_tests = set(self.tests.keys())
        
        # Filter by name pattern
        if name_pattern:
            import fnmatch
            pattern_matches = {
                name for name in self.tests.keys() 
                if fnmatch.fnmatch(name, name_pattern)
            }
            matching_tests &= pattern_matches
        
        # Filter by tags
        if tags:
            for tag in tags:
                tag_matches = self.tags_index.get(tag, set())
                matching_tests &= tag_matches
        
        # Filter by compliance level
        if compliance_level:
            compliance_matches = self.compliance_index.get(compliance_level, set())
            matching_tests &= compliance_matches
        
        # Filter by mode
        if mode:
            mode_matches = set(self.find_tests_by_mode(mode))
            matching_tests &= mode_matches
        
        return list(matching_tests)

    def get_statistics(self) -> Dict[str, Any]:
        """Get registry statistics."""
        total_scenarios = 0
        initiator_scenarios = 0
        responder_scenarios = 0
        
        for test in self.tests.values():
            for scenarios in test.initiator_scenarios.values():
                initiator_scenarios += len(scenarios)
                total_scenarios += len(scenarios)
            
            for scenarios in test.responder_scenarios.values():
                responder_scenarios += len(scenarios)
                total_scenarios += len(scenarios)
        
        return {
            "total_tests": len(self.tests),
            "total_scenarios": total_scenarios,
            "initiator_scenarios": initiator_scenarios,
            "responder_scenarios": responder_scenarios,
            "unique_tags": len(self.tags_index),
            "compliance_levels": list(self.compliance_index.keys()),
        }


class ScenarioLoader:
    """
    Loader for test scenarios from various sources.
    
    Supports loading from individual files, directories, and with
    various filtering and validation options.
    """

    def __init__(self, registry: Optional[ScenarioRegistry] = None):
        """
        Initialize the scenario loader.
        
        Args:
            registry: Optional registry to use (creates new one if not provided)
        """
        self.registry = registry or ScenarioRegistry()
        self.loaded_files: Set[Path] = set()
        
        logger.debug("Initialized ScenarioLoader")

    def load_file(self, filepath: Union[str, Path]) -> Test:
        """
        Load a single test file.
        
        Args:
            filepath: Path to the test file
            
        Returns:
            Loaded Test object
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise ScenarioParsingError(f"Test file not found: {filepath}")
        
        if not filepath.suffix.lower() in ['.yml', '.yaml']:
            raise ScenarioParsingError(f"Unsupported file format: {filepath.suffix}")
        
        logger.info(f"Loading test file: {filepath}")
        
        try:
            # Parse the test file
            parser = TestParser(filepath)
            test = parser.get_test_definition()
            
            # Create metadata
            metadata = self._create_metadata(test, filepath)
            
            # Register in registry
            self.registry.register_test(test, metadata)
            self.loaded_files.add(filepath)
            
            logger.info(f"Successfully loaded test '{test.name}' from {filepath}")
            return test
            
        except Exception as e:
            logger.error(f"Failed to load test file {filepath}: {e}")
            raise ScenarioParsingError(f"Error loading {filepath}: {e}")

    def load_directory(
        self, 
        directory: Union[str, Path], 
        pattern: str = "*.yml",
        recursive: bool = True
    ) -> List[Test]:
        """
        Load all test files from a directory.
        
        Args:
            directory: Directory path
            pattern: File pattern to match
            recursive: Whether to search recursively
            
        Returns:
            List of loaded Test objects
        """
        directory = Path(directory)
        
        if not directory.exists():
            raise ScenarioParsingError(f"Directory not found: {directory}")
        
        if not directory.is_dir():
            raise ScenarioParsingError(f"Path is not a directory: {directory}")
        
        logger.info(f"Loading tests from directory: {directory} (pattern: {pattern}, recursive: {recursive})")
        
        # Find test files
        if recursive:
            search_pattern = f"**/{pattern}"
        else:
            search_pattern = pattern
        
        test_files = list(directory.glob(search_pattern))
        
        if not test_files:
            logger.warning(f"No test files found in {directory} matching pattern {pattern}")
            return []
        
        logger.info(f"Found {len(test_files)} test files")
        
        # Load each file
        loaded_tests = []
        failed_files = []
        
        for test_file in test_files:
            try:
                test = self.load_file(test_file)
                loaded_tests.append(test)
            except Exception as e:
                logger.error(f"Failed to load {test_file}: {e}")
                failed_files.append((test_file, str(e)))
        
        logger.info(f"Successfully loaded {len(loaded_tests)} tests, {len(failed_files)} failed")
        
        if failed_files:
            logger.warning("Failed files:")
            for file_path, error in failed_files:
                logger.warning(f"  {file_path}: {error}")
        
        return loaded_tests

    def load_test_list(self, test_names: List[str], search_paths: List[Union[str, Path]]) -> List[Test]:
        """
        Load specific tests by name from search paths.
        
        Args:
            test_names: List of test names to load
            search_paths: List of directories to search
            
        Returns:
            List of loaded Test objects
        """
        logger.info(f"Loading {len(test_names)} specific tests from {len(search_paths)} search paths")
        
        loaded_tests = []
        not_found = []
        
        for test_name in test_names:
            found = False
            
            for search_path in search_paths:
                search_path = Path(search_path)
                
                # Try different file patterns
                patterns = [
                    f"{test_name}.yml",
                    f"{test_name}.yaml",
                    f"**/{test_name}.yml",
                    f"**/{test_name}.yaml",
                ]
                
                for pattern in patterns:
                    matching_files = list(search_path.glob(pattern))
                    
                    if matching_files:
                        try:
                            test = self.load_file(matching_files[0])
                            loaded_tests.append(test)
                            found = True
                            break
                        except Exception as e:
                            logger.error(f"Failed to load {test_name} from {matching_files[0]}: {e}")
                
                if found:
                    break
            
            if not found:
                not_found.append(test_name)
        
        if not_found:
            logger.warning(f"Could not find tests: {not_found}")
        
        logger.info(f"Successfully loaded {len(loaded_tests)} out of {len(test_names)} requested tests")
        return loaded_tests

    def _create_metadata(self, test: Test, filepath: Path) -> ScenarioMetadata:
        """Create metadata for a test."""
        # Get file stats
        stat = filepath.stat()
        
        # Extract additional metadata from test if available
        tags = getattr(test, 'tags', [])
        anssi_requirement = getattr(test, 'anssi_requirement', None)
        compliance_level = getattr(test, 'compliance_level', 'standard')
        
        return ScenarioMetadata(
            name=test.name,
            description=test.description,
            version=test.version,
            file_path=filepath,
            anssi_requirement=anssi_requirement,
            tags=tags,
            compliance_level=compliance_level,
            created_at=None,  # Would need to be in the file
            modified_at=str(stat.st_mtime),
        )

    def get_registry(self) -> ScenarioRegistry:
        """Get the scenario registry."""
        return self.registry

    def get_loaded_files(self) -> List[Path]:
        """Get list of loaded files."""
        return list(self.loaded_files)

    def reload_file(self, filepath: Union[str, Path]) -> Test:
        """
        Reload a specific file.
        
        Args:
            filepath: Path to the file to reload
            
        Returns:
            Reloaded Test object
        """
        filepath = Path(filepath)
        
        if filepath in self.loaded_files:
            logger.info(f"Reloading test file: {filepath}")
        else:
            logger.info(f"Loading new test file: {filepath}")
        
        return self.load_file(filepath)
