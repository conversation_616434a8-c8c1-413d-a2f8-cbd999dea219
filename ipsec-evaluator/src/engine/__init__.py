"""
Test execution engine for IPsec Evaluator.

This module contains the core test execution components:
- Orchestrator: Manages test execution and infrastructure
- Tester: Executes individual test scenarios
- Checker: Validates compliance and analyzes results
- Hook system: Provides callback capabilities
"""

from .orchestrator import EnhancedOrchestrator
from .tester import EnhancedTester
from .checker import Enhanced<PERSON>hecker

# from .hooks import HookManager  # TODO: Implement hook manager

# Backward compatibility aliases
Orchestrator = EnhancedOrchestrator
Tester = EnhancedTester
Checker = EnhancedChecker

__all__ = [
    "EnhancedOrchestrator",
    "EnhancedTester",
    "EnhancedChe<PERSON>",
    "Orchestrator",  # Backward compatibility
    "Tester",  # Backward compatibility
    "Checker",  # Backward compatibility
]
