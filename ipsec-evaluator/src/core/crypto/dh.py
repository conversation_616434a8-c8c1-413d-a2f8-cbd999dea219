"""
Enhanced Diff<PERSON>-<PERSON> key exchange implementation with comprehensive curve support.

This module provides DH group implementations for IKEv2 key exchange with:
- Complete secp curve family support
- Brainpool curve support for ANSSI compliance
- Curve25519/X25519 support
- MODP finite field groups
- Comprehensive validation and error handling
"""

from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
from dataclasses import dataclass

from cryptography.hazmat.primitives.asymmetric import dh, ec, x25519, x448
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend

from ...utils.logging import get_logger

logger = get_logger(__name__)


class CurveType(Enum):
    """Types of elliptic curves."""

    SECP = "secp"
    BRAINPOOL = "brainpool"
    EDWARDS = "edwards"
    MONTGOMERY = "montgomery"


class DHGroupType(Enum):
    """Types of DH groups."""

    MODP = "modp"
    ECP = "ecp"
    CURVE25519 = "curve25519"
    CURVE448 = "curve448"


@dataclass
class DHGroupInfo:
    """Information about a DH group."""

    group_id: int
    name: str
    group_type: DHGroupType
    curve_type: Optional[CurveType] = None
    key_size: Optional[int] = None
    anssi_approved: bool = False
    description: str = ""


class EnhancedDHManager:
    """
    Enhanced Diffie-Hellman key exchange manager with comprehensive curve support.

    This class provides support for:
    - Complete secp curve family (secp192r1, secp224r1, secp256r1, secp384r1, secp521r1)
    - Brainpool curves for ANSSI compliance (brainpoolP256r1, brainpoolP384r1, brainpoolP512r1)
    - Modern curves (Curve25519, Curve448)
    - Traditional MODP finite field groups
    - Comprehensive validation and metadata
    """

    def __init__(self):
        self._backend = default_backend()

        # Comprehensive DH group registry
        self._supported_groups = {
            # MODP Groups (RFC 3526)
            14: DHGroupInfo(
                14,
                "MODP-2048",
                DHGroupType.MODP,
                key_size=2048,
                description="2048-bit MODP Group",
            ),
            15: DHGroupInfo(
                15,
                "MODP-3072",
                DHGroupType.MODP,
                key_size=3072,
                description="3072-bit MODP Group",
            ),
            16: DHGroupInfo(
                16,
                "MODP-4096",
                DHGroupType.MODP,
                key_size=4096,
                description="4096-bit MODP Group",
            ),
            17: DHGroupInfo(
                17,
                "MODP-6144",
                DHGroupType.MODP,
                key_size=6144,
                description="6144-bit MODP Group",
            ),
            18: DHGroupInfo(
                18,
                "MODP-8192",
                DHGroupType.MODP,
                key_size=8192,
                description="8192-bit MODP Group",
            ),
            # secp Curves (NIST/SECG)
            19: DHGroupInfo(
                19,
                "secp256r1",
                DHGroupType.ECP,
                CurveType.SECP,
                256,
                anssi_approved=True,
                description="256-bit random ECP group (P-256)",
            ),
            20: DHGroupInfo(
                20,
                "secp384r1",
                DHGroupType.ECP,
                CurveType.SECP,
                384,
                anssi_approved=True,
                description="384-bit random ECP group (P-384)",
            ),
            21: DHGroupInfo(
                21,
                "secp521r1",
                DHGroupType.ECP,
                CurveType.SECP,
                521,
                anssi_approved=True,
                description="521-bit random ECP group (P-521)",
            ),
            25: DHGroupInfo(
                25,
                "secp192r1",
                DHGroupType.ECP,
                CurveType.SECP,
                192,
                description="192-bit random ECP group (P-192)",
            ),
            26: DHGroupInfo(
                26,
                "secp224r1",
                DHGroupType.ECP,
                CurveType.SECP,
                224,
                description="224-bit random ECP group (P-224)",
            ),
            # Brainpool Curves (ANSSI approved)
            27: DHGroupInfo(
                27,
                "brainpoolP224r1",
                DHGroupType.ECP,
                CurveType.BRAINPOOL,
                224,
                anssi_approved=True,
                description="224-bit Brainpool ECP group",
            ),
            28: DHGroupInfo(
                28,
                "brainpoolP256r1",
                DHGroupType.ECP,
                CurveType.BRAINPOOL,
                256,
                anssi_approved=True,
                description="256-bit Brainpool ECP group",
            ),
            29: DHGroupInfo(
                29,
                "brainpoolP384r1",
                DHGroupType.ECP,
                CurveType.BRAINPOOL,
                384,
                anssi_approved=True,
                description="384-bit Brainpool ECP group",
            ),
            30: DHGroupInfo(
                30,
                "brainpoolP512r1",
                DHGroupType.ECP,
                CurveType.BRAINPOOL,
                512,
                anssi_approved=True,
                description="512-bit Brainpool ECP group",
            ),
            # Modern Curves
            31: DHGroupInfo(
                31,
                "Curve25519",
                DHGroupType.CURVE25519,
                CurveType.MONTGOMERY,
                255,
                description="Curve25519 (RFC 7748)",
            ),
            32: DHGroupInfo(
                32,
                "Curve448",
                DHGroupType.CURVE448,
                CurveType.EDWARDS,
                448,
                description="Curve448 (RFC 7748)",
            ),
        }

        logger.info(
            f"Enhanced DH Manager initialized with {len(self._supported_groups)} groups"
        )

    def get_supported_groups(self) -> List[int]:
        """Get list of supported DH group IDs."""
        return list(self._supported_groups.keys())

    def get_group_info(self, group_id: int) -> Optional[DHGroupInfo]:
        """Get information about a DH group."""
        return self._supported_groups.get(group_id)

    def get_anssi_approved_groups(self) -> List[int]:
        """Get list of ANSSI-approved DH groups."""
        return [
            group_id
            for group_id, info in self._supported_groups.items()
            if info.anssi_approved
        ]

    def get_groups_by_type(self, group_type: DHGroupType) -> List[int]:
        """Get groups of a specific type."""
        return [
            group_id
            for group_id, info in self._supported_groups.items()
            if info.group_type == group_type
        ]

    def get_groups_by_curve_type(self, curve_type: CurveType) -> List[int]:
        """Get groups of a specific curve type."""
        return [
            group_id
            for group_id, info in self._supported_groups.items()
            if info.curve_type == curve_type
        ]

    def validate_group(self, group_id: int) -> bool:
        """Validate if a DH group is supported."""
        return group_id in self._supported_groups

    def is_anssi_approved(self, group_id: int) -> bool:
        """Check if a DH group is ANSSI approved."""
        info = self._supported_groups.get(group_id)
        return info.anssi_approved if info else False

    def generate_keypair(self, group_id: int) -> Tuple[bytes, bytes]:
        """
        Generate DH key pair for specified group.

        Args:
            group_id: DH group number

        Returns:
            Tuple of (private_key_bytes, public_key_bytes)
        """
        if not self.validate_group(group_id):
            raise ValueError(f"Unsupported DH group: {group_id}")

        group_info = self._supported_groups[group_id]

        logger.debug(f"Generating keypair for {group_info.name} (group {group_id})")

        if group_info.group_type == DHGroupType.MODP:
            return self._generate_modp_keypair(group_id)
        elif group_info.group_type == DHGroupType.ECP:
            return self._generate_ecp_keypair(group_id)
        elif group_info.group_type == DHGroupType.CURVE25519:
            return self._generate_curve25519_keypair()
        elif group_info.group_type == DHGroupType.CURVE448:
            return self._generate_curve448_keypair()
        else:
            raise ValueError(f"DH group type {group_info.group_type} not implemented")

    def compute_shared_secret(
        self, group_id: int, private_key: bytes, peer_public_key: bytes
    ) -> bytes:
        """
        Compute DH shared secret.

        Args:
            group_id: DH group number
            private_key: Own private key
            peer_public_key: Peer's public key

        Returns:
            Shared secret bytes
        """
        if not self.validate_group(group_id):
            raise ValueError(f"Unsupported DH group: {group_id}")

        group_info = self._supported_groups[group_id]

        logger.debug(
            f"Computing shared secret for {group_info.name} (group {group_id})"
        )

        if group_info.group_type == DHGroupType.MODP:
            return self._compute_modp_shared_secret(
                group_id, private_key, peer_public_key
            )
        elif group_info.group_type == DHGroupType.ECP:
            return self._compute_ecp_shared_secret(
                group_id, private_key, peer_public_key
            )
        elif group_info.group_type == DHGroupType.CURVE25519:
            return self._compute_curve25519_shared_secret(private_key, peer_public_key)
        elif group_info.group_type == DHGroupType.CURVE448:
            return self._compute_curve448_shared_secret(private_key, peer_public_key)
        else:
            raise ValueError(f"DH group type {group_info.group_type} not implemented")

    def _generate_modp_keypair(self, group: int) -> Tuple[bytes, bytes]:
        """Generate MODP (finite field) DH key pair."""
        # Get parameters for the group
        parameters = self._get_modp_parameters(group)

        # Generate private key
        private_key = parameters.generate_private_key()
        public_key = private_key.public_key()

        # Serialize keys
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        return private_bytes, public_bytes

    def _generate_ecp_keypair(self, group_id: int) -> Tuple[bytes, bytes]:
        """Generate ECP (elliptic curve) DH key pair."""
        curve = self._get_ecp_curve(group_id)
        group_info = self._supported_groups[group_id]

        logger.debug(
            f"Generating {group_info.curve_type.value} keypair for {group_info.name}"
        )

        # Generate private key
        private_key = ec.generate_private_key(curve, self._backend)
        public_key = private_key.public_key()

        # Serialize keys
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.X962,
            format=serialization.PublicFormat.UncompressedPoint,
        )

        logger.debug(
            f"Generated ECP keypair: private={len(private_bytes)} bytes, public={len(public_bytes)} bytes"
        )

        return private_bytes, public_bytes

    def _generate_curve25519_keypair(self) -> Tuple[bytes, bytes]:
        """Generate Curve25519 key pair."""
        logger.debug("Generating Curve25519 keypair")

        private_key = x25519.X25519PrivateKey.generate()
        public_key = private_key.public_key()

        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.Raw, format=serialization.PublicFormat.Raw
        )

        logger.debug(
            f"Generated Curve25519 keypair: private={len(private_bytes)} bytes, public={len(public_bytes)} bytes"
        )

        return private_bytes, public_bytes

    def _generate_curve448_keypair(self) -> Tuple[bytes, bytes]:
        """Generate Curve448 key pair."""
        logger.debug("Generating Curve448 keypair")

        private_key = x448.X448PrivateKey.generate()
        public_key = private_key.public_key()

        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.Raw, format=serialization.PublicFormat.Raw
        )

        logger.debug(
            f"Generated Curve448 keypair: private={len(private_bytes)} bytes, public={len(public_bytes)} bytes"
        )

        return private_bytes, public_bytes

    def _compute_modp_shared_secret(
        self, group: int, private_key: bytes, peer_public_key: bytes
    ) -> bytes:
        """Compute MODP shared secret."""
        # Load private key
        private_key_obj = serialization.load_der_private_key(
            private_key, password=None, backend=self._backend
        )

        # Load peer public key
        peer_public_key_obj = serialization.load_der_public_key(
            peer_public_key, backend=self._backend
        )

        # Compute shared secret
        shared_secret = private_key_obj.exchange(peer_public_key_obj)
        return shared_secret

    def _compute_ecp_shared_secret(
        self, group: int, private_key: bytes, peer_public_key: bytes
    ) -> bytes:
        """Compute ECP shared secret."""
        # Load private key
        private_key_obj = serialization.load_der_private_key(
            private_key, password=None, backend=self._backend
        )

        # Load peer public key (from uncompressed point format)
        curve = self._get_ecp_curve(group)
        peer_public_key_obj = ec.EllipticCurvePublicKey.from_encoded_point(
            curve, peer_public_key
        )

        # Compute shared secret
        shared_secret = private_key_obj.exchange(ec.ECDH(), peer_public_key_obj)
        return shared_secret

    def _compute_curve25519_shared_secret(
        self, private_key: bytes, peer_public_key: bytes
    ) -> bytes:
        """Compute Curve25519 shared secret."""
        logger.debug("Computing Curve25519 shared secret")

        # Load private key
        private_key_obj = x25519.X25519PrivateKey.from_private_bytes(private_key)

        # Load peer public key
        peer_public_key_obj = x25519.X25519PublicKey.from_public_bytes(peer_public_key)

        # Compute shared secret
        shared_secret = private_key_obj.exchange(peer_public_key_obj)

        logger.debug(f"Computed Curve25519 shared secret: {len(shared_secret)} bytes")
        return shared_secret

    def _compute_curve448_shared_secret(
        self, private_key: bytes, peer_public_key: bytes
    ) -> bytes:
        """Compute Curve448 shared secret."""
        logger.debug("Computing Curve448 shared secret")

        # Load private key
        private_key_obj = x448.X448PrivateKey.from_private_bytes(private_key)

        # Load peer public key
        peer_public_key_obj = x448.X448PublicKey.from_public_bytes(peer_public_key)

        # Compute shared secret
        shared_secret = private_key_obj.exchange(peer_public_key_obj)

        logger.debug(f"Computed Curve448 shared secret: {len(shared_secret)} bytes")
        return shared_secret

    def _get_modp_parameters(self, group: int) -> dh.DHParameters:
        """Get DH parameters for MODP group."""
        # This is a simplified implementation
        # In practice, you'd use the exact parameters from RFC 3526
        if group == 14:  # MODP-2048
            return dh.generate_parameters(
                generator=2, key_size=2048, backend=self._backend
            )
        elif group == 15:  # MODP-3072
            return dh.generate_parameters(
                generator=2, key_size=3072, backend=self._backend
            )
        elif group == 16:  # MODP-4096
            return dh.generate_parameters(
                generator=2, key_size=4096, backend=self._backend
            )
        else:
            raise ValueError(f"MODP group {group} not supported")

    def _get_ecp_curve(self, group_id: int) -> ec.EllipticCurve:
        """Get elliptic curve for ECP group with comprehensive secp and brainpool support."""

        # secp curves (NIST/SECG)
        if group_id == 19:  # secp256r1 (P-256)
            return ec.SECP256R1()
        elif group_id == 20:  # secp384r1 (P-384)
            return ec.SECP384R1()
        elif group_id == 21:  # secp521r1 (P-521)
            return ec.SECP521R1()
        elif group_id == 25:  # secp192r1 (P-192)
            return ec.SECP192R1()
        elif group_id == 26:  # secp224r1 (P-224)
            return ec.SECP224R1()

        # Brainpool curves (ANSSI approved)
        elif group_id == 27:  # brainpoolP224r1
            return ec.BrainpoolP224R1()
        elif group_id == 28:  # brainpoolP256r1
            return ec.BrainpoolP256R1()
        elif group_id == 29:  # brainpoolP384r1
            return ec.BrainpoolP384R1()
        elif group_id == 30:  # brainpoolP512r1
            return ec.BrainpoolP512R1()

        else:
            group_info = self._supported_groups.get(group_id)
            group_name = group_info.name if group_info else f"group {group_id}"
            raise ValueError(f"ECP curve {group_name} not supported")

    def get_curve_info(self, group_id: int) -> Dict[str, Any]:
        """Get detailed information about a curve."""
        if not self.validate_group(group_id):
            raise ValueError(f"Unsupported DH group: {group_id}")

        group_info = self._supported_groups[group_id]

        info = {
            "group_id": group_id,
            "name": group_info.name,
            "type": group_info.group_type.value,
            "key_size": group_info.key_size,
            "anssi_approved": group_info.anssi_approved,
            "description": group_info.description,
        }

        if group_info.curve_type:
            info["curve_type"] = group_info.curve_type.value

            # Add curve-specific information
            if group_info.group_type == DHGroupType.ECP:
                try:
                    curve = self._get_ecp_curve(group_id)
                    info["curve_class"] = curve.__class__.__name__
                    info["field_size"] = curve.field_size
                except Exception as e:
                    info["curve_error"] = str(e)

        return info

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about supported DH groups."""
        stats = {
            "total_groups": len(self._supported_groups),
            "anssi_approved": len(self.get_anssi_approved_groups()),
            "by_type": {},
            "by_curve_type": {},
            "key_sizes": set(),
        }

        # Count by group type
        for group_type in DHGroupType:
            groups = self.get_groups_by_type(group_type)
            stats["by_type"][group_type.value] = len(groups)

        # Count by curve type
        for curve_type in CurveType:
            groups = self.get_groups_by_curve_type(curve_type)
            stats["by_curve_type"][curve_type.value] = len(groups)

        # Collect key sizes
        for info in self._supported_groups.values():
            if info.key_size:
                stats["key_sizes"].add(info.key_size)

        stats["key_sizes"] = sorted(list(stats["key_sizes"]))

        return stats


# Maintain backward compatibility
DHManager = EnhancedDHManager
