"""
Comprehensive hook system for IKEv2 protocol implementation.

This module provides a flexible and extensible hook system that allows
users to inject custom logic at various points in the IKEv2 protocol
execution, enabling advanced testing scenarios and compliance checking.
"""

import asyncio
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field

from scapy.contrib.ikev2 import IKEv2

from ...utils.logging import get_logger

logger = get_logger(__name__)


class HookType(Enum):
    """Types of hooks available in the IKEv2 protocol."""

    # Packet-level hooks
    PRE_PACKET = "pre_packet"
    POST_PACKET = "post_packet"
    PACKET_SPECIFIC = "packet_specific"  # Triggered on specific packet numbers

    # Exchange-level hooks
    PRE_EXCHANGE = "pre_exchange"
    POST_EXCHANGE = "post_exchange"
    EXCHANGE_IKE_SA_INIT = "exchange_ike_sa_init"
    EXCHANGE_IKE_AUTH = "exchange_ike_auth"
    EXCHANGE_CREATE_CHILD_SA = "exchange_create_child_sa"
    EXCHANGE_INFORMATIONAL = "exchange_informational"

    # Universal hooks
    UNIVERSAL = "universal"  # Called for every packet

    # Error handling hooks
    ERROR = "error"
    TIMEOUT = "timeout"

    # State transition hooks
    STATE_CHANGE = "state_change"

    # Cryptographic hooks
    PRE_ENCRYPT = "pre_encrypt"
    POST_DECRYPT = "post_decrypt"
    KEY_DERIVATION = "key_derivation"


class HookPriority(Enum):
    """Hook execution priorities."""

    HIGHEST = 0
    HIGH = 10
    NORMAL = 50
    LOW = 90
    LOWEST = 100


@dataclass
class HookContext:
    """Context information passed to hook functions."""

    # Basic context
    exchange_type: Optional["ExchangeType"] = None
    message_id: int = 0
    role: Optional["IKEv2Role"] = None
    packet_number: int = 0
    timestamp: datetime = field(default_factory=datetime.now)

    # Packet information
    packet: Optional[IKEv2] = None
    response_packet: Optional[IKEv2] = None
    source_addr: Optional[tuple] = None

    # State information
    current_state: Optional["IKEv2State"] = None
    previous_state: Optional["IKEv2State"] = None

    # Error information
    error: Optional[Exception] = None

    # Cryptographic context
    encryption_key: Optional[bytes] = None
    integrity_key: Optional[bytes] = None

    # Custom metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Results from previous hooks
    hook_results: Dict[str, Any] = field(default_factory=dict)

    def add_metadata(self, key: str, value: Any):
        """Add metadata to the context."""
        self.metadata[key] = value

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata from the context."""
        return self.metadata.get(key, default)


@dataclass
class HookRegistration:
    """Registration information for a hook."""

    hook_type: HookType
    callback: Callable[[HookContext], Any]
    priority: HookPriority = HookPriority.NORMAL
    name: Optional[str] = None
    description: Optional[str] = None

    # Conditional execution
    packet_number: Optional[int] = None  # For packet-specific hooks
    exchange_type: Optional["ExchangeType"] = None  # For exchange-specific hooks
    condition: Optional[Callable[[HookContext], bool]] = None  # Custom condition

    # Execution tracking
    execution_count: int = 0
    last_executed: Optional[datetime] = None
    total_execution_time: float = 0.0

    def should_execute(self, context: HookContext) -> bool:
        """Determine if this hook should execute for the given context."""

        # Check packet number condition
        if (
            self.packet_number is not None
            and context.packet_number != self.packet_number
        ):
            return False

        # Check exchange type condition
        if (
            self.exchange_type is not None
            and context.exchange_type != self.exchange_type
        ):
            return False

        # Check custom condition
        if self.condition is not None:
            try:
                return self.condition(context)
            except Exception as e:
                logger.warning(f"Hook condition failed for {self.name}: {e}")
                return False

        return True

    def update_execution_stats(self, execution_time: float):
        """Update execution statistics."""
        self.execution_count += 1
        self.last_executed = datetime.now()
        self.total_execution_time += execution_time


class IKEv2HookManager:
    """
    Manages hook registration and execution for IKEv2 protocol.

    This class provides a comprehensive hook system that allows users to:
    - Register callbacks for specific protocol events
    - Execute hooks with proper error handling
    - Collect and analyze hook execution statistics
    - Support conditional hook execution
    """

    def __init__(self):
        """Initialize the hook manager."""
        self.hooks: Dict[HookType, List[HookRegistration]] = {
            hook_type: [] for hook_type in HookType
        }
        self.global_hooks: List[HookRegistration] = []
        self.execution_stats: Dict[str, Any] = {
            "total_executions": 0,
            "total_execution_time": 0.0,
            "hook_errors": 0,
            "hook_timeouts": 0,
        }

        logger.debug("IKEv2HookManager initialized")

    def register_hook(
        self,
        hook_type: HookType,
        callback: Callable[[HookContext], Any],
        priority: HookPriority = HookPriority.NORMAL,
        name: Optional[str] = None,
        description: Optional[str] = None,
        **conditions,
    ) -> str:
        """
        Register a hook callback.

        Args:
            hook_type: Type of hook to register
            callback: Callback function to execute
            priority: Execution priority
            name: Optional name for the hook
            description: Optional description
            **conditions: Additional conditions (packet_number, exchange_type, etc.)

        Returns:
            Hook registration ID
        """
        if name is None:
            name = f"{hook_type.value}_{len(self.hooks[hook_type])}"

        registration = HookRegistration(
            hook_type=hook_type,
            callback=callback,
            priority=priority,
            name=name,
            description=description,
            **conditions,
        )

        # Add to appropriate hook list
        self.hooks[hook_type].append(registration)

        # Sort by priority
        self.hooks[hook_type].sort(key=lambda h: h.priority.value)

        logger.debug(f"Registered hook '{name}' for {hook_type.value}")
        return name

    def register_packet_hook(
        self,
        packet_number: int,
        callback: Callable[[HookContext], Any],
        priority: HookPriority = HookPriority.NORMAL,
        name: Optional[str] = None,
    ) -> str:
        """Register a hook for a specific packet number."""
        return self.register_hook(
            HookType.PACKET_SPECIFIC,
            callback,
            priority,
            name,
            packet_number=packet_number,
        )

    def register_exchange_hook(
        self,
        exchange_type: "ExchangeType",
        callback: Callable[[HookContext], Any],
        priority: HookPriority = HookPriority.NORMAL,
        name: Optional[str] = None,
    ) -> str:
        """Register a hook for a specific exchange type."""
        return self.register_hook(
            HookType.PRE_EXCHANGE, callback, priority, name, exchange_type=exchange_type
        )

    def register_universal_hook(
        self,
        callback: Callable[[HookContext], Any],
        priority: HookPriority = HookPriority.NORMAL,
        name: Optional[str] = None,
    ) -> str:
        """Register a universal hook that executes for every packet."""
        return self.register_hook(HookType.UNIVERSAL, callback, priority, name)

    async def execute_hooks(
        self, hook_type: HookType, context: HookContext, timeout: float = 5.0
    ) -> Dict[str, Any]:
        """
        Execute all registered hooks of a specific type.

        Args:
            hook_type: Type of hooks to execute
            context: Context information for the hooks
            timeout: Maximum execution time per hook

        Returns:
            Dictionary of hook results
        """
        results = {}

        # Get hooks to execute
        hooks_to_execute = []

        # Add specific hooks
        for hook in self.hooks[hook_type]:
            if hook.should_execute(context):
                hooks_to_execute.append(hook)

        # Add universal hooks for packet-related events
        if hook_type in [
            HookType.PRE_PACKET,
            HookType.POST_PACKET,
            HookType.PACKET_SPECIFIC,
        ]:
            for hook in self.hooks[HookType.UNIVERSAL]:
                if hook.should_execute(context):
                    hooks_to_execute.append(hook)

        # Execute hooks
        for hook in hooks_to_execute:
            try:
                start_time = asyncio.get_event_loop().time()

                # Execute hook with timeout
                if asyncio.iscoroutinefunction(hook.callback):
                    result = await asyncio.wait_for(
                        hook.callback(context), timeout=timeout
                    )
                else:
                    result = hook.callback(context)

                execution_time = asyncio.get_event_loop().time() - start_time

                # Update statistics
                hook.update_execution_stats(execution_time)
                self.execution_stats["total_executions"] += 1
                self.execution_stats["total_execution_time"] += execution_time

                # Store result
                results[hook.name] = result
                context.hook_results[hook.name] = result

                logger.debug(f"Executed hook '{hook.name}' in {execution_time:.3f}s")

            except asyncio.TimeoutError:
                logger.warning(f"Hook '{hook.name}' timed out after {timeout}s")
                self.execution_stats["hook_timeouts"] += 1
                results[hook.name] = {"error": "timeout"}

            except Exception as e:
                logger.error(f"Hook '{hook.name}' failed: {e}")
                self.execution_stats["hook_errors"] += 1
                results[hook.name] = {"error": str(e)}

        return results

    def unregister_hook(self, hook_name: str) -> bool:
        """
        Unregister a hook by name.

        Args:
            hook_name: Name of the hook to unregister

        Returns:
            True if hook was found and removed
        """
        for hook_type, hook_list in self.hooks.items():
            for i, hook in enumerate(hook_list):
                if hook.name == hook_name:
                    del hook_list[i]
                    logger.debug(f"Unregistered hook '{hook_name}'")
                    return True

        logger.warning(f"Hook '{hook_name}' not found for unregistration")
        return False

    def get_hook_statistics(self) -> Dict[str, Any]:
        """Get comprehensive hook execution statistics."""
        hook_stats = {}

        for hook_type, hook_list in self.hooks.items():
            hook_stats[hook_type.value] = {
                "registered_count": len(hook_list),
                "hooks": [
                    {
                        "name": hook.name,
                        "execution_count": hook.execution_count,
                        "total_execution_time": hook.total_execution_time,
                        "average_execution_time": (
                            hook.total_execution_time / hook.execution_count
                            if hook.execution_count > 0
                            else 0
                        ),
                        "last_executed": (
                            hook.last_executed.isoformat()
                            if hook.last_executed
                            else None
                        ),
                    }
                    for hook in hook_list
                ],
            }

        return {"global_stats": self.execution_stats, "hook_types": hook_stats}

    def get_execution_count(self) -> int:
        """Get total number of hook executions."""
        return self.execution_stats["total_executions"]

    def clear_hooks(self, hook_type: Optional[HookType] = None):
        """
        Clear registered hooks.

        Args:
            hook_type: Specific hook type to clear, or None to clear all
        """
        if hook_type:
            self.hooks[hook_type].clear()
            logger.debug(f"Cleared all {hook_type.value} hooks")
        else:
            for hook_list in self.hooks.values():
                hook_list.clear()
            logger.debug("Cleared all hooks")

    def list_hooks(self) -> Dict[str, List[str]]:
        """List all registered hooks by type."""
        return {
            hook_type.value: [hook.name for hook in hook_list]
            for hook_type, hook_list in self.hooks.items()
            if hook_list
        }
