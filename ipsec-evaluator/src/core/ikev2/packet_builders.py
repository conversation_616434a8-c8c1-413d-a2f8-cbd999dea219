"""
High-level IKEv2 packet builders.

This module provides high-level methods for building complete IKEv2 packets
for different exchange types, complementing the low-level payload builders.
"""

import secrets
from typing import Dict, List, Optional
from scapy.contrib.ikev2 import IKEv2

from ...utils.logging import get_logger

logger = get_logger(__name__)


class IKEv2PacketBuilders:
    """
    Mixin class providing high-level IKEv2 packet building methods.
    
    This class contains methods for building complete IKEv2 packets
    for different exchange types.
    """

    def build_ike_sa_init_request(
        self,
        spi_i: bytes,
        message_id: int,
        nonce: bytes,
        dh_group: int,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete IKE_SA_INIT request packet.

        Args:
            spi_i: Initiator SPI
            message_id: Message ID
            nonce: Initiator nonce
            dh_group: Diffie-Hellman group
            notify_extras: Optional notify payloads

        Returns:
            Complete IKE_SA_INIT packet
        """
        logger.debug("Building IKE_SA_INIT request")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=b'\x00' * 8,
            next_payload="SA",
            exch_type="IKE_SA_INIT",
            flags="Initiator",
            id=message_id,
        )

        # Add SA payload
        packet /= self.build_sa_payload(
            proposal_type="IKE",
            next_payload="KE",
        )

        # Add KE payload
        packet /= self.build_ke_payload(
            dh_group=dh_group,
            next_payload="Nonce",
        )

        # Add Nonce payload
        next_pld = "Notify" if notify_extras else "None"
        packet /= self.build_nonce_payload(
            nonce=nonce,
            next_payload=next_pld,
        )

        # Add notify payloads if any
        if notify_extras:
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                packet /= self.build_notify_payload(
                    notify_type=notify.get("type", "INITIAL_CONTACT"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )

        return packet

    def build_ike_sa_init_response(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        nonce: bytes,
        dh_group: int,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete IKE_SA_INIT response packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            nonce: Responder nonce
            dh_group: Diffie-Hellman group
            notify_extras: Optional notify payloads

        Returns:
            Complete IKE_SA_INIT response packet
        """
        logger.debug("Building IKE_SA_INIT response")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=spi_r,
            next_payload="SA",
            exch_type="IKE_SA_INIT",
            flags="Response",
            id=message_id,
        )

        # Add SA payload
        packet /= self.build_sa_payload(
            proposal_type="IKE",
            next_payload="KE",
        )

        # Add KE payload
        packet /= self.build_ke_payload(
            dh_group=dh_group,
            next_payload="Nonce",
        )

        # Add Nonce payload
        next_pld = "Notify" if notify_extras else "None"
        packet /= self.build_nonce_payload(
            nonce=nonce,
            next_payload=next_pld,
        )

        # Add notify payloads if any
        if notify_extras:
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                packet /= self.build_notify_payload(
                    notify_type=notify.get("type", "NAT_DETECTION_SOURCE_IP"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )

        return packet

    def build_ike_auth_request(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        auth_method: int,
        identity_data: str = "<EMAIL>",
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete IKE_AUTH request packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            auth_method: Authentication method
            identity_data: Identity data
            notify_extras: Optional notify payloads

        Returns:
            Complete IKE_AUTH packet
        """
        logger.debug("Building IKE_AUTH request")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=spi_r,
            next_payload="Encrypted",
            exch_type="IKE_AUTH",
            flags="Initiator",
            id=message_id,
        )

        # Build encrypted payload content
        encrypted_content = self.build_id_payload(
            id_type="ID_RFC822_ADDR",
            id_data=identity_data,
            next_payload="AUTH",
        )

        encrypted_content /= self.build_auth_payload(
            auth_method=auth_method,
            auth_data=b"dummy_auth_data",  # Would be real auth data
            next_payload="Notify" if notify_extras else "None",
        )

        # Add notify payloads if any
        if notify_extras:
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                encrypted_content /= self.build_notify_payload(
                    notify_type=notify.get("type", "INITIAL_CONTACT"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )

        # Add encrypted payload (simplified - would need real encryption)
        from scapy.contrib.ikev2 import IKEv2_Encrypted
        packet /= IKEv2_Encrypted(
            next_payload="None",
            encrypted_data=bytes(encrypted_content),
        )

        return packet

    def build_ike_auth_response(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        auth_method: int,
        identity_data: str = "<EMAIL>",
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete IKE_AUTH response packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            auth_method: Authentication method
            identity_data: Identity data
            notify_extras: Optional notify payloads

        Returns:
            Complete IKE_AUTH response packet
        """
        logger.debug("Building IKE_AUTH response")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=spi_r,
            next_payload="Encrypted",
            exch_type="IKE_AUTH",
            flags="Response",
            id=message_id,
        )

        # Build encrypted payload content
        encrypted_content = self.build_id_payload(
            id_type="ID_RFC822_ADDR",
            id_data=identity_data,
            next_payload="AUTH",
        )

        encrypted_content /= self.build_auth_payload(
            auth_method=auth_method,
            auth_data=b"dummy_auth_data",  # Would be real auth data
            next_payload="Notify" if notify_extras else "None",
        )

        # Add notify payloads if any
        if notify_extras:
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                encrypted_content /= self.build_notify_payload(
                    notify_type=notify.get("type", "INITIAL_CONTACT"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )

        # Add encrypted payload (simplified - would need real encryption)
        from scapy.contrib.ikev2 import IKEv2_Encrypted
        packet /= IKEv2_Encrypted(
            next_payload="None",
            encrypted_data=bytes(encrypted_content),
        )

        return packet

    def build_create_child_sa_request(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        child_spi: bytes,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete CREATE_CHILD_SA request packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            child_spi: Child SA SPI
            notify_extras: Optional notify payloads

        Returns:
            Complete CREATE_CHILD_SA packet
        """
        logger.debug("Building CREATE_CHILD_SA request")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=spi_r,
            next_payload="Encrypted",
            exch_type="CREATE_CHILD_SA",
            flags="Initiator",
            id=message_id,
        )

        # Build encrypted payload content
        encrypted_content = self.build_sa_payload(
            proposal_type="ESP",
            next_payload="Nonce",
        )

        encrypted_content /= self.build_nonce_payload(
            nonce=secrets.token_bytes(32),
            next_payload="TSi",
        )

        encrypted_content /= self.build_ts_payload(
            ts_type="TSi",
            ip_range=("***********", "*************"),
            port_range=(0, 65535),
            next_payload="TSr",
        )

        encrypted_content /= self.build_ts_payload(
            ts_type="TSr",
            ip_range=("10.0.0.0", "**********"),
            port_range=(0, 65535),
            next_payload="Notify" if notify_extras else "None",
        )

        # Add notify payloads if any
        if notify_extras:
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                encrypted_content /= self.build_notify_payload(
                    notify_type=notify.get("type", "USE_TRANSPORT_MODE"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )

        # Add encrypted payload (simplified - would need real encryption)
        from scapy.contrib.ikev2 import IKEv2_Encrypted
        packet /= IKEv2_Encrypted(
            next_payload="None",
            encrypted_data=bytes(encrypted_content),
        )

        return packet

    def build_create_child_sa_response(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        child_spi: bytes,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete CREATE_CHILD_SA response packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            child_spi: Child SA SPI
            notify_extras: Optional notify payloads

        Returns:
            Complete CREATE_CHILD_SA response packet
        """
        logger.debug("Building CREATE_CHILD_SA response")

        # Similar to request but with Response flag
        packet = self.build_create_child_sa_request(
            spi_i, spi_r, message_id, child_spi, notify_extras
        )
        
        # Update flags to Response
        packet.flags = "Response"
        
        return packet

    def build_informational_request(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete INFORMATIONAL request packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            notify_extras: Optional notify payloads

        Returns:
            Complete INFORMATIONAL packet
        """
        logger.debug("Building INFORMATIONAL request")

        # Build IKE header
        packet = IKEv2(
            init_SPI=spi_i,
            resp_SPI=spi_r,
            next_payload="Encrypted" if notify_extras else "None",
            exch_type="INFORMATIONAL",
            flags="Initiator",
            id=message_id,
        )

        # Add encrypted payload if there are notify payloads
        if notify_extras:
            encrypted_content = None
            for i, notify in enumerate(notify_extras):
                next_pld = "Notify" if i < len(notify_extras) - 1 else "None"
                notify_payload = self.build_notify_payload(
                    notify_type=notify.get("type", "INITIAL_CONTACT"),
                    notify_data=notify.get("data", b""),
                    next_payload=next_pld,
                )
                
                if encrypted_content is None:
                    encrypted_content = notify_payload
                else:
                    encrypted_content /= notify_payload

            # Add encrypted payload (simplified - would need real encryption)
            from scapy.contrib.ikev2 import IKEv2_Encrypted
            packet /= IKEv2_Encrypted(
                next_payload="None",
                encrypted_data=bytes(encrypted_content),
            )

        return packet

    def build_informational_response(
        self,
        spi_i: bytes,
        spi_r: bytes,
        message_id: int,
        notify_extras: Optional[List[Dict]] = None,
    ) -> IKEv2:
        """
        Build a complete INFORMATIONAL response packet.

        Args:
            spi_i: Initiator SPI
            spi_r: Responder SPI
            message_id: Message ID
            notify_extras: Optional notify payloads

        Returns:
            Complete INFORMATIONAL response packet
        """
        logger.debug("Building INFORMATIONAL response")

        # Similar to request but with Response flag
        packet = self.build_informational_request(
            spi_i, spi_r, message_id, notify_extras
        )
        
        # Update flags to Response
        packet.flags = "Response"
        
        return packet
