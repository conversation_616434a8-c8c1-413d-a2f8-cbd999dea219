#!/usr/bin/env python3
"""
IKEv2 Implementation Integration Example

This script demonstrates how to integrate and use the new IKEv2 exchange handlers
and initiator methods with the existing ipsec-evaluator architecture.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from ipsec_evaluator.core.ikev2.protocol_updated import (
    IKEv2Protocol, 
    IKEv2Role, 
    IKEv2SecurityAssociation
)
from ipsec_evaluator.core.ikev2.state import IKEv2State, ExchangeType
from ipsec_evaluator.core.ikev2.hooks import IKEv2HookManager, HookType
from ipsec_evaluator.utils.logging import get_logger

logger = get_logger(__name__)


async def example_initiator_flow():
    """
    Example of a complete IKE negotiation flow as initiator.
    """
    logger.info("=== IKEv2 Initiator Flow Example ===")
    
    # Initialize protocol as initiator
    protocol = IKEv2Protocol(
        role=IKEv2Role.INITIATOR,
        local_ip="***********00",
        remote_ip="***********",
        local_port=500,
        remote_port=500
    )
    
    logger.info(f"Initial state: {protocol.state_machine.current_state.value}")
    
    try:
        # Step 1: Initiate IKE_SA_INIT
        logger.info("Step 1: Initiating IKE_SA_INIT")
        init_packet = await protocol.initiate_ike_sa_init()
        logger.info(f"Generated IKE_SA_INIT packet: {init_packet.summary()}")
        logger.info(f"State after INIT: {protocol.state_machine.current_state.value}")
        
        # In a real implementation, you would send this packet and wait for response
        # For this example, we'll simulate receiving a response
        
        # Step 2: Simulate processing IKE_SA_INIT response
        logger.info("Step 2: Simulating IKE_SA_INIT response processing")
        # This would normally be called when a response is received
        # response = await protocol.process_packet(received_response, ("***********", 500))
        
        # For demo, manually transition state
        protocol.state_machine.transition_to(IKEv2State.SA_INIT_COMPLETED)
        logger.info(f"State after INIT response: {protocol.state_machine.current_state.value}")
        
        # Step 3: Initiate IKE_AUTH
        logger.info("Step 3: Initiating IKE_AUTH")
        auth_packet = await protocol.initiate_ike_auth()
        logger.info(f"Generated IKE_AUTH packet: {auth_packet.summary()}")
        logger.info(f"State after AUTH: {protocol.state_machine.current_state.value}")
        
        # Step 4: Simulate IKE_AUTH response
        logger.info("Step 4: Simulating IKE_AUTH response processing")
        protocol.state_machine.transition_to(IKEv2State.ESTABLISHED)
        logger.info(f"State after AUTH response: {protocol.state_machine.current_state.value}")
        
        # Step 5: Create Child SA
        logger.info("Step 5: Creating Child SA")
        child_packet = await protocol.initiate_create_child_sa()
        logger.info(f"Generated CREATE_CHILD_SA packet: {child_packet.summary()}")
        
        # Step 6: Send informational message
        logger.info("Step 6: Sending informational message")
        info_packet = await protocol.initiate_informational()
        logger.info(f"Generated INFORMATIONAL packet: {info_packet.summary()}")
        
        # Display statistics
        stats = protocol.get_statistics()
        logger.info("=== Final Statistics ===")
        for key, value in stats.items():
            logger.info(f"{key}: {value}")
            
    except Exception as e:
        logger.error(f"Error in initiator flow: {e}")
        raise


async def example_responder_flow():
    """
    Example of handling packets as a responder.
    """
    logger.info("=== IKEv2 Responder Flow Example ===")
    
    # Initialize protocol as responder
    protocol = IKEv2Protocol(
        role=IKEv2Role.RESPONDER,
        local_ip="***********",
        remote_ip="***********00",
        local_port=500,
        remote_port=500
    )
    
    logger.info(f"Initial state: {protocol.state_machine.current_state.value}")
    
    try:
        # Simulate receiving and processing packets
        # In a real implementation, these would be actual received packets
        
        logger.info("Simulating packet reception and processing...")
        
        # For this example, we'll show the structure without actual packets
        logger.info("Responder would:")
        logger.info("1. Receive IKE_SA_INIT request")
        logger.info("2. Process with _handle_ike_sa_init_responder()")
        logger.info("3. Generate IKE_SA_INIT response")
        logger.info("4. Receive IKE_AUTH request")
        logger.info("5. Process with _handle_ike_auth_responder()")
        logger.info("6. Generate IKE_AUTH response")
        logger.info("7. Handle CREATE_CHILD_SA and INFORMATIONAL as needed")
        
        # Display available exchange handlers
        logger.info("=== Available Exchange Handlers ===")
        handlers = [
            "_handle_ike_sa_init",
            "_handle_ike_auth", 
            "_handle_create_child_sa",
            "_handle_informational"
        ]
        for handler in handlers:
            logger.info(f"✓ {handler}")
            
    except Exception as e:
        logger.error(f"Error in responder flow: {e}")
        raise


async def example_hook_integration():
    """
    Example of using the hook system with the new handlers.
    """
    logger.info("=== Hook System Integration Example ===")
    
    # Create hook manager
    hook_manager = IKEv2HookManager()
    
    # Add some example hooks
    async def pre_exchange_hook(context):
        logger.info(f"PRE_EXCHANGE hook: {context.exchange_type.value}")
        
    async def post_packet_hook(context):
        logger.info(f"POST_PACKET hook: packet #{context.packet_number}")
    
    # Register hooks
    hook_manager.register_hook(HookType.PRE_EXCHANGE, pre_exchange_hook)
    hook_manager.register_hook(HookType.POST_PACKET, post_packet_hook)
    
    # Initialize protocol with hook manager
    protocol = IKEv2Protocol(
        role=IKEv2Role.INITIATOR,
        local_ip="***********00",
        remote_ip="***********",
        hook_manager=hook_manager
    )
    
    logger.info("Protocol initialized with hook manager")
    logger.info(f"Registered hooks: {len(hook_manager.hooks)} total")
    
    # The hooks will be automatically called during exchange processing
    logger.info("Hooks will be executed automatically during:")
    logger.info("- Exchange initiation (PRE_EXCHANGE)")
    logger.info("- Packet processing (PRE_PACKET, POST_PACKET)")
    logger.info("- Exchange completion (POST_EXCHANGE)")
    logger.info("- Error handling (ERROR)")


def example_state_validation():
    """
    Example of state machine validation and exchange checking.
    """
    logger.info("=== State Machine Validation Example ===")
    
    protocol = IKEv2Protocol(
        role=IKEv2Role.INITIATOR,
        local_ip="***********00",
        remote_ip="***********"
    )
    
    # Check what exchanges can be initiated in different states
    states_to_check = [
        IKEv2State.INITIAL,
        IKEv2State.SA_INIT_COMPLETED,
        IKEv2State.ESTABLISHED
    ]
    
    exchanges_to_check = [
        ExchangeType.IKE_SA_INIT,
        ExchangeType.IKE_AUTH,
        ExchangeType.CREATE_CHILD_SA,
        ExchangeType.INFORMATIONAL
    ]
    
    for state in states_to_check:
        protocol.state_machine.current_state = state
        logger.info(f"\nIn state {state.value}:")
        
        for exchange in exchanges_to_check:
            can_initiate = protocol.can_initiate_exchange(exchange)
            status = "✓" if can_initiate else "✗"
            logger.info(f"  {status} {exchange.value}")


async def main():
    """
    Main function demonstrating all examples.
    """
    logger.info("IKEv2 Implementation Integration Examples")
    logger.info("=" * 50)
    
    try:
        # Run examples
        await example_initiator_flow()
        print("\n" + "=" * 50 + "\n")
        
        await example_responder_flow()
        print("\n" + "=" * 50 + "\n")
        
        await example_hook_integration()
        print("\n" + "=" * 50 + "\n")
        
        example_state_validation()
        
        logger.info("\n✓ All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
