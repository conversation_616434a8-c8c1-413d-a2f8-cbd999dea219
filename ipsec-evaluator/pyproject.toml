[tool.poetry]
name = "ipsec-evaluator"
version = "0.1.0"
description = "Modern IPsec compliance testing tool for ANSSI IPsecDR corpus"
authors = ["IPsec Evaluator Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
packages = [{include = "ipsec_evaluator", from = "src"}]

[project]
name = "ipsec-evaluator"
version = "0.1.0"
description = "Modern IPsec compliance testing tool for ANSSI IPsecDR corpus"
authors = [
    { name = "IPsec Evaluator Team", email = "<EMAIL>" }
]
readme = "README.md"
license = "MIT"
packages = [{include = "ipsec_evaluator", from = "src"}]

[project.scripts]
ipsec_evaluator = "ipsec_evaluator.cli:cli_main"


[tool.poetry.dependencies]
python = "^3.12"
# Core dependencies
asyncio-mqtt = "^0.16.2"
cryptography = "^43.0.1"
pydantic = "^2.9.2"
pydantic-settings = "^2.6.0"
pyyaml = "^6.0.2"
rich = "^13.9.4"
scapy = "^2.6.1"
typer = "^0.15"
# Async support
aiofiles = "^24.1.0"
httpx = "^0.27.2"
# Networking
netaddr = "^1.3.0"
# Configuration
toml = "^0.10.2"
# Logging
structlog = "^24.4.0"
# Data processing
pandas = "^2.2.0"
numpy = "^1.26.0"
# Testing framework
pytest-asyncio = "^0.24.0"
# Performance monitoring
psutil = "^6.1.0"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
pytest-cov = "^5.0.0"
pytest-xdist = "^3.6.0"
pytest-mock = "^3.14.0"
pytest-rerunfailures = "^14.0"
pytest-randomly = "^3.15.0"
# Code quality
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.11.2"
pre-commit = "^4.0.1"
ruff = "^0.8.4"
bandit = "^1.8.0"
safety = "^3.2.0"
# Documentation
mkdocs = "^1.6.1"
mkdocs-material = "^9.5.42"
mkdocstrings = {extras = ["python"], version = "^0.26.2"}
# Development tools
ipython = "^8.29.0"
jupyter = "^1.1.1"
# Performance profiling
py-spy = "^0.3.14"
memory-profiler = "^0.61.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Tool configurations
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
    | \.mypy_cache
    | \.venv
    | build
    | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ipsec_evaluator"]

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "ARG001", # unused-function-argument
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "Q", # flake8-quotes
    "PL", # pylint
    "PT", # flake8-pytest-style
    "RUF", # ruff-specific rules
]
ignore = [
    "E501",  # line too long, handled by black
    "PLR0913", # too many arguments
    "PLR0912", # too many branches
    "PLR0915", # too many statements
    "PLR2004", # magic value used in comparison
]
exclude = [
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    ".venv",
    "__pycache__",
    "build",
    "dist",
]

[tool.ruff.per-file-ignores]
"tests/*" = ["ARG001", "PLR2004", "SIM117"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config --tb=short"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "compliance: Compliance validation tests",
    "anssi: ANSSI compliance tests",
    "crypto: Cryptographic tests",
    "performance: Performance tests",
    "real_world: Real-world infrastructure tests",
    "slow: Slow running tests",
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
    "*/site-packages/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
skips = ["B101", "B601"]

[tool.safety]
ignore = []
